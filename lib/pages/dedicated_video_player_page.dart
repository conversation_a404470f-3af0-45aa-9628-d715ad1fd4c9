import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:vimeo_video_player/vimeo_video_player.dart';
import '../models/course_video.dart';
import '../models/user_profile.dart';
import '../services/api_service.dart';
import '../services/video_streak_service.dart';
import '../utils/video_security_helper.dart';
import '../widgets/official_vimeo_player.dart';

class DedicatedVideoPlayerPage extends StatefulWidget {
  final CourseVideo video;
  final UserProfile? userProfile;

  const DedicatedVideoPlayerPage({
    Key? key,
    required this.video,
    this.userProfile,
  }) : super(key: key);

  @override
  State<DedicatedVideoPlayerPage> createState() => _DedicatedVideoPlayerPageState();
}

class _DedicatedVideoPlayerPageState extends State<DedicatedVideoPlayerPage>
    with TickerProviderStateMixin {
  late WebViewController _webViewController;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentPosition = 0;
  int _duration = 0;
  bool _isPlaying = false;
  bool _isCompleted = false;
  bool _showControls = true;
  bool _isFullscreen = false;
  double _currentPlaybackSpeed = 1.0;
  bool _isBuffering = false;

  // Animation controllers
  late AnimationController _controlsAnimationController;
  late AnimationController _watermarkAnimationController;
  late Animation<double> _controlsOpacity;
  late Animation<double> _watermarkOpacity;

  // Security and tracking
  final ApiService _apiService = ApiService();
  String? _secureEmbedUrl;
  bool _isSecurityValidated = false;

  // Progress tracking
  int _lastProgressUpdate = 0;
  static const int _progressUpdateInterval = 5; // seconds
  bool _streakChecked = false;

  // User details for watermark
  String _userDisplayName = '';
  String _userPhoneNumber = '';
  String _userPhoneLast4Digits = '';

  // Auto-hide controls timer
  Timer? _hideControlsTimer;

  // Skip controls state
  bool _showSkipForward = false;
  bool _showSkipBackward = false;
  Timer? _skipIndicatorTimer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadUserDetails();
    _initializePlayer();
    _setupAutoHideControls();
  }

  void _initializeAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _watermarkAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _controlsOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));

    _watermarkOpacity = Tween<double>(
      begin: 0.0,
      end: 0.7,
    ).animate(CurvedAnimation(
      parent: _watermarkAnimationController,
      curve: Curves.easeInOut,
    ));

    // Show controls initially
    _controlsAnimationController.forward();
    // Show watermark after a brief delay
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        _watermarkAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _hideControlsTimer?.cancel();
    _skipIndicatorTimer?.cancel();
    _controlsAnimationController.dispose();
    _watermarkAnimationController.dispose();

    // Restore overlays and orientation for extra reliability
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    super.dispose();
  }

  Future<void> _loadUserDetails() async {
    try {
      if (widget.userProfile != null) {
        setState(() {
          _userDisplayName = widget.userProfile!.name;
          _userPhoneNumber = widget.userProfile!.phoneNumber;
          _userPhoneLast4Digits = _extractLast4Digits(_userPhoneNumber);
        });
      } else {
        // Fetch user profile from API
        final response = await _apiService.makeApiRequest('profile.php');
        if (response['success'] == true && response['profile'] != null) {
          setState(() {
            _userDisplayName = response['profile']['name'] ?? 'User';
            _userPhoneNumber = response['profile']['phone_number'] ?? '';
            _userPhoneLast4Digits = _extractLast4Digits(_userPhoneNumber);
          });
        }
      }
    } catch (e) {
      debugPrint('Failed to load user details: $e');
      setState(() {
        _userDisplayName = 'User';
        _userPhoneNumber = '';
        _userPhoneLast4Digits = '';
      });
    }
  }

  String _extractLast4Digits(String phoneNumber) {
    if (phoneNumber.isEmpty) return '';
    // Remove any non-digit characters
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    // Return last 4 digits
    return digitsOnly.length >= 4 ? digitsOnly.substring(digitsOnly.length - 4) : digitsOnly;
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Validate video security and get secure embed URL
      await _validateVideoSecurity();

      if (_isSecurityValidated && _secureEmbedUrl != null) {
        await _setupVimeoPlayer();
      } else {
        _handleError('Video access denied or security validation failed');
      }
    } catch (e) {
      _handleError('Failed to initialize video player: $e');
    }
  }

  Future<void> _validateVideoSecurity() async {
    try {
      // Extract Vimeo ID from the video URL
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);

      if (vimeoId == null) {
        throw Exception('Invalid Vimeo video ID');
      }

      // Validate domain and get secure embed URL
      _secureEmbedUrl = await VideoSecurityHelper.getSecureEmbedUrl(
        vimeoId: vimeoId,
        videoId: widget.video.id,
        userId: await _getCurrentUserId(),
      );

      if (_secureEmbedUrl != null) {
        _isSecurityValidated = true;

        // Log video access
        await VideoSecurityHelper.logVideoAccess(
          vimeoId: vimeoId,
          videoId: widget.video.id,
          userId: await _getCurrentUserId(),
          action: 'access',
        );
      }
    } catch (e) {
      debugPrint('Security validation failed: $e');
      _isSecurityValidated = false;
    }
  }

  Future<int> _getCurrentUserId() async {
    try {
      final response = await _apiService.makeApiRequest('profile.php');
      if (response['success'] == true && response['profile'] != null) {
        return response['profile']['id'] ?? 1;
      }
      return 1; // Fallback
    } catch (e) {
      debugPrint('Failed to get current user ID: $e');
      return 1; // Fallback
    }
  }

  Future<void> _setupVimeoPlayer() async {
    try {
      if (kIsWeb) {
        await _setupWebPlayer();
      } else {
        await _setupMobilePlayer();
      }
    } catch (e) {
      _handleError('Failed to setup player: $e');
    }
  }

  Future<void> _setupWebPlayer() async {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _isBuffering = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
              _isBuffering = false;
            });
            _setupWebPlayerListeners();
            // Auto-play when ready
            Future.delayed(const Duration(milliseconds: 1000), () {
              _playVideo();
            });
          },
          onWebResourceError: (WebResourceError error) {
            _handleError('Web player error: ${error.description}');
          },
        ),
      )
      // Enable inline media playback and gesture permissions for Vimeo seeking
      ..enableZoom(false)
      // Add JavaScript handlers for proper communication
      ..addJavaScriptChannel(
        'FlutterVideoPlayer',
        onMessageReceived: (JavaScriptMessage message) {
          final data = message.message.split(':');
          final event = data[0];

          switch (event) {
            case 'play':
              _onVimeoPlay();
              break;
            case 'pause':
              _onVimeoPause();
              break;
            case 'timeupdate':
              if (data.length > 1) {
                final seconds = double.tryParse(data[1])?.round() ?? 0;
                _onVimeoTimeUpdate(seconds);
              }
              break;
            case 'ended':
              _onVimeoFinish();
              break;
            case 'bufferstart':
              _onBufferStart();
              break;
            case 'bufferend':
              _onBufferEnd();
              break;
            case 'duration':
              if (data.length > 1) {
                final duration = double.tryParse(data[1])?.round() ?? 0;
                setState(() {
                  _duration = duration;
                });
              }
              break;
          }
        },
      );

    // Load the enhanced secure Vimeo embed with auto-play
    final embedHtml = _buildCustomEmbedHtml();
    await _webViewController.loadHtmlString(embedHtml);
  }

  Future<void> _setupMobilePlayer() async {
    try {
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);

      if (vimeoId == null) {
        throw Exception('Invalid Vimeo ID');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      _handleError('Failed to setup mobile player: $e');
    }
  }

  void _setupWebPlayerListeners() {
    // Enhanced JavaScript integration for custom controls
    _webViewController.runJavaScript('''
      try {
        var iframe = document.querySelector('iframe');
        var player = new Vimeo.Player(iframe);

        // Store player reference globally for control methods
        window.vimeoPlayer = player;

        // Event listeners using proper Flutter communication
        player.on('play', function() {
          FlutterVideoPlayer.postMessage('play');
        });

        player.on('pause', function() {
          FlutterVideoPlayer.postMessage('pause');
        });

        player.on('timeupdate', function(data) {
          FlutterVideoPlayer.postMessage('timeupdate:' + data.seconds);
        });

        player.on('ended', function() {
          FlutterVideoPlayer.postMessage('ended');
        });

        player.on('bufferstart', function() {
          FlutterVideoPlayer.postMessage('bufferstart');
        });

        player.on('bufferend', function() {
          FlutterVideoPlayer.postMessage('bufferend');
        });

        // Get video metadata
        player.getDuration().then(function(duration) {
          FlutterVideoPlayer.postMessage('duration:' + duration);
        }).catch(function(error) {
          console.log('Failed to get duration:', error);
        });

        // Auto-play
        player.play().catch(function(error) {
          console.log('Auto-play failed:', error);
        });

        console.log('Vimeo player setup complete');
      } catch (error) {
        console.error('Error setting up Vimeo player:', error);
      }
    ''');
  }

  String _buildCustomEmbedHtml() {
    final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);

    // Build enhanced Vimeo embed URL with all required settings
    final embedUrl = 'https://player.vimeo.com/video/$vimeoId'
        '?autoplay=1'           // Enable autoplay
        '&controls=0'           // Hide default controls (we use custom)
        '&title=0'              // Hide video title
        '&byline=0'             // Hide video byline
        '&portrait=0'           // Hide author portrait
        '&sharing=0'            // Disable sharing buttons
        '&download=0'           // Disable download option
        '&upload=0'             // Disable upload functionality
        '&responsive=1'         // Enable responsive sizing
        '&dnt=1'                // Enable Do Not Track
        '&background=0'         // Disable background mode
        '&muted=0'              // Start unmuted
        '&loop=0'               // Disable looping
        '&speed=1'              // Enable speed controls
        '&quality=auto'         // Auto quality selection
        '&transparent=0'        // Disable transparency
        '&color=ff0000';        // Set accent color to red

    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
        <meta charset="utf-8">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body {
                margin: 0;
                padding: 0;
                background: #000;
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                width: 100vw;
                height: 100vh;
            }
            iframe {
                width: 100%;
                height: 100vh;
                border: none;
                display: block;
                position: absolute;
                top: 0;
                left: 0;
            }
            .loading {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-size: 16px;
                z-index: 1000;
                text-align: center;
            }
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 3px solid rgba(255,255,255,0.3);
                border-radius: 50%;
                border-top-color: #fff;
                animation: spin 1s ease-in-out infinite;
                margin: 0 auto 16px;
            }
            @keyframes spin {
                to { transform: rotate(360deg); }
            }
        </style>
        <script src="https://player.vimeo.com/api/player.js"></script>
    </head>
    <body>
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <div>Loading video...</div>
        </div>
        <iframe src="$embedUrl"
                frameborder="0"
                allow="autoplay; fullscreen; picture-in-picture; accelerometer; gyroscope; microphone; camera; encrypted-media; web-share"
                allowfullscreen
                webkitallowfullscreen
                mozallowfullscreen
                webkitPlaysinline="true"
                playsinline="true"
                referrerpolicy="origin"
                onload="document.getElementById('loading').style.display='none'">
        </iframe>
    </body>
    </html>
    ''';
  }

  // Enhanced event handlers
  void _onVimeoPlay() {
    setState(() {
      _isPlaying = true;
      _isBuffering = false;
    });
    _trackVideoProgress();
    _logVideoEvent('play');
    _startAutoHideTimer(); // Start auto-hide timer when playing
  }

  void _onVimeoPause() {
    setState(() {
      _isPlaying = false;
    });
    _logVideoEvent('pause');
    _showControlsPermanently(); // Show controls permanently when paused
  }

  void _onVimeoTimeUpdate(int position) {
    setState(() {
      _currentPosition = position;
    });

    // Update progress every 5 seconds
    if (position - _lastProgressUpdate >= _progressUpdateInterval) {
      _updateVideoProgress();
      _lastProgressUpdate = position;
    }

    // Check for completion (80% watched)
    if (_duration > 0 && position >= (_duration * 0.8) && !_isCompleted) {
      _onVideoCompleted();
    }

    // Check for streak qualification (50% watched)
    if (_duration > 0 && position >= (_duration * 0.5) && !_streakChecked) {
      _checkStreakQualification();
    }
  }

  void _onVimeoFinish() {
    _onVideoCompleted();
  }

  void _onVideoCompleted() {
    setState(() {
      _isCompleted = true;
      _isPlaying = false;
    });
    _updateVideoProgress(isCompleted: true);
    _logVideoEvent('complete');
  }

  Future<void> _checkStreakQualification() async {
    if (_streakChecked) return;

    setState(() {
      _streakChecked = true;
    });

    try {
      // Import the video streak service
      final VideoStreakService streakService = VideoStreakService();

      // Process video completion for streak tracking (50% completion)
      final result = await streakService.processVideoCompletion(
        widget.video.id,
        _currentPosition,
        widget.video.durationMinutes ?? 0,
      );

      if (result.message != null) {
        // Show motivational message for both new streaks and already earned streaks
        _showStreakMessage(result.message!, result.newStreak, result.highestStreak);
      }
    } catch (e) {
      debugPrint('Error checking streak qualification: $e');
    }
  }

  void _showStreakMessage(String message, int currentStreak, int highestStreak) {
    // Show a brief motivational message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _onBufferStart() {
    setState(() {
      _isBuffering = true;
    });
    _cancelAutoHideTimer(); // Don't hide controls while buffering
  }

  void _onBufferEnd() {
    setState(() {
      _isBuffering = false;
    });
    if (_isPlaying) {
      _startAutoHideTimer(); // Resume auto-hide timer after buffering
    }
  }

  Future<void> _trackVideoProgress() async {
    while (_isPlaying && mounted) {
      await Future.delayed(const Duration(seconds: 5));
      if (_isPlaying && mounted) {
        await _updateVideoProgress();
      }
    }
  }

  Future<void> _updateVideoProgress({bool isCompleted = false}) async {
    try {
      await _apiService.updateVideoProgress(
        videoId: widget.video.id,
        watchDurationSeconds: _currentPosition,
        lastPositionSeconds: _currentPosition,
        isCompleted: isCompleted,
      );
    } catch (e) {
      debugPrint('Failed to update video progress: $e');
    }
  }

  Future<void> _logVideoEvent(String action) async {
    try {
      final vimeoId = VideoSecurityHelper.extractVimeoId(widget.video.videoUrl);
      if (vimeoId != null) {
        await VideoSecurityHelper.logVideoAccess(
          vimeoId: vimeoId,
          videoId: widget.video.id,
          userId: await _getCurrentUserId(),
          action: action,
        );
      }
    } catch (e) {
      // Do NOT clear tokens or logout on video event logging errors
      // Video event logging is for analytics only and should not affect authentication
      debugPrint('Failed to log video event: $e');
    }
  }

  void _handleError(String message) {
    setState(() {
      _hasError = true;
      _errorMessage = message;
      _isLoading = false;
      _isBuffering = false;
    });
  }

  void _togglePlayPause() {
    debugPrint('Toggle play/pause - Current state: $_isPlaying');
    if (_isPlaying) {
      _pauseVideo();
    } else {
      _playVideo();
    }
  }

  void _playVideo() {
    debugPrint('Play video called');
    if (kIsWeb) {
      _webViewController.runJavaScript('''
        if (window.vimeoPlayer) {
          console.log('Attempting to play video');
          window.vimeoPlayer.play().then(function() {
            console.log('Video play successful');
          }).catch(function(error) {
            console.log('Play failed:', error);
          });
        } else {
          console.log('Vimeo player not available');
        }
      ''');
    }
    // Optimistically update state for immediate feedback
    setState(() {
      _isPlaying = true;
    });
  }

  void _pauseVideo() {
    debugPrint('Pause video called');
    if (kIsWeb) {
      _webViewController.runJavaScript('''
        if (window.vimeoPlayer) {
          console.log('Attempting to pause video');
          window.vimeoPlayer.pause().then(function() {
            console.log('Video pause successful');
          }).catch(function(error) {
            console.log('Pause failed:', error);
          });
        } else {
          console.log('Vimeo player not available');
        }
      ''');
    }
    // Optimistically update state for immediate feedback
    setState(() {
      _isPlaying = false;
    });
  }

  void _seekTo(int seconds) {
    if (kIsWeb) {
      _webViewController.runJavaScript('''
        if (window.vimeoPlayer) {
          window.vimeoPlayer.setCurrentTime($seconds).catch(function(error) {
            console.log('Seek failed:', error);
          });
        }
      ''');
    }
  }

  void _changePlaybackSpeed(double speed) {
    setState(() {
      _currentPlaybackSpeed = speed;
    });

    debugPrint('Changing playback speed to: ${speed}x');

    if (kIsWeb) {
      _webViewController.runJavaScript('''
        console.log('Attempting to change playback speed to $speed');
        if (window.vimeoPlayer) {
          window.vimeoPlayer.setPlaybackRate($speed).then(function() {
            console.log('Playback speed changed successfully to $speed');
          }).catch(function(error) {
            console.log('Speed change failed:', error);
          });
        } else {
          console.log('Vimeo player not available for speed change');
        }
      ''');
    } else {
      // For mobile VimeoVideoPlayer, we need to use a different approach
      // Since the VimeoVideoPlayer widget doesn't directly support speed changes,
      // we'll need to reload the player with speed parameter
      debugPrint('Mobile playback speed change not directly supported by VimeoVideoPlayer');

      // Show a message to the user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Playback speed changed to ${speed}x'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.black87,
          ),
        );
      }
    }
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });

    // Note: Orientation changes are handled by Vimeo player's native fullscreen
    debugPrint('DedicatedVideoPlayer: Fullscreen toggled - $_isFullscreen');
  }

  void _resetOrientation() async {
    // Force portrait orientation with proper cleanup
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    // First reset to allow all orientations briefly
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // Small delay to ensure the system processes the change
    await Future.delayed(const Duration(milliseconds: 100));

    // Then force portrait only
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    debugPrint('Orientation reset to portrait');
  }

  void _setupAutoHideControls() {
    // This method is called during initialization
    // The actual auto-hide logic is handled by _startAutoHideTimer
  }

  void _startAutoHideTimer() {
    _cancelAutoHideTimer();
    if (_isPlaying && !_isBuffering) {
      _hideControlsTimer = Timer(const Duration(seconds: 3), () {
        if (mounted && _isPlaying && _showControls && !_isBuffering) {
          setState(() {
            _showControls = false;
          });
          _controlsAnimationController.reverse();
        }
      });
    }
  }

  void _cancelAutoHideTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = null;
  }

  void _showControlsTemporarily() {
    if (!_showControls) {
      setState(() {
        _showControls = true;
      });
      _controlsAnimationController.forward();
    }
    _startAutoHideTimer(); // Restart the auto-hide timer
  }

  void _showControlsPermanently() {
    _cancelAutoHideTimer();
    setState(() {
      _showControls = true;
    });
    _controlsAnimationController.forward();
  }

  // Netflix-style skip controls
  void _skipForward() {
    final newPosition = (_currentPosition + 10).clamp(0, _duration);
    _seekTo(newPosition);
    _showSkipIndicator(true);
  }

  void _skipBackward() {
    final newPosition = (_currentPosition - 10).clamp(0, _duration);
    _seekTo(newPosition);
    _showSkipIndicator(false);
  }

  void _showSkipIndicator(bool isForward) {
    _skipIndicatorTimer?.cancel();

    setState(() {
      if (isForward) {
        _showSkipForward = true;
        _showSkipBackward = false;
      } else {
        _showSkipBackward = true;
        _showSkipForward = false;
      }
    });

    // Hide indicator after 1 second
    _skipIndicatorTimer = Timer(const Duration(milliseconds: 1000), () {
      if (mounted) {
        setState(() {
          _showSkipForward = false;
          _showSkipBackward = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Video player
            _buildVideoPlayer(),

            // Interactive touch layer for skip controls (when controls are visible)
            if (_showControls) _buildInteractiveTouchLayer(),

            // Dynamic watermark
            _buildDynamicWatermark(),

            // Buffering indicator
            if (_isBuffering) _buildBufferingIndicator(),

            // Custom controls overlay
            if (_showControls) _buildCustomControls(),

            // Skip indicators (above everything)
            if (_showSkipForward) _buildSkipIndicator(true),
            if (_showSkipBackward) _buildSkipIndicator(false),

            // Error overlay
            if (_hasError) _buildErrorOverlay(),

            // Loading overlay
            if (_isLoading) _buildLoadingOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: _buildPlayerWidget(),
    );
  }

  Widget _buildPlayerWidget() {
    if (_hasError || _isLoading) {
      return const SizedBox.shrink();
    }
    if (widget.video.videoProvider == 'vimeo') {
      return AspectRatio(
        aspectRatio: 16 / 9,
        child: OfficialVimeoPlayer(
          video: widget.video,
          autoPlay: true,
        ),
      );
    }
    return _buildErrorWidget();
  }

  Widget _buildDynamicWatermark() {
    if (_userDisplayName.isEmpty) return const SizedBox.shrink();

    return AnimatedBuilder(
      animation: _watermarkOpacity,
      builder: (context, child) {
        return Positioned(
          bottom: _isFullscreen ? 80 : 120,
          right: 16,
          child: Opacity(
            opacity: _watermarkOpacity.value,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _userDisplayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (_userPhoneLast4Digits.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      '••••$_userPhoneLast4Digits',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCustomControls() {
    return AnimatedBuilder(
      animation: _controlsOpacity,
      builder: (context, child) {
        return Opacity(
          opacity: _controlsOpacity.value,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.7),
                  Colors.transparent,
                  Colors.black.withOpacity(0.7),
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
            ),
            child: Column(
              children: [
                // Top controls
                _buildTopControls(),

                // Center play/pause button
                Expanded(
                  child: Center(
                    child: _buildCenterPlayButton(),
                  ),
                ),

                // Bottom controls
                _buildBottomControls(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Faded minimal back button
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  Icons.arrow_back,
                  color: Colors.white.withOpacity(0.7),
                  size: 24,
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Video title
          Expanded(
            child: Text(
              widget.video.title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          const SizedBox(width: 16),

          // Fullscreen button
          IconButton(
            onPressed: () {
              _toggleFullscreen();
              _startAutoHideTimer(); // Restart timer on interaction
            },
            icon: Icon(
              _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
              color: Colors.white,
              size: 24,
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.black.withOpacity(0.5),
              padding: const EdgeInsets.all(12),
              minimumSize: const Size(48, 48),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCenterPlayButton() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          _togglePlayPause();
          _startAutoHideTimer(); // Restart timer on interaction
        },
        borderRadius: BorderRadius.circular(50),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.7),
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            _isPlaying ? Icons.pause : Icons.play_arrow,
            color: Colors.white,
            size: 40,
          ),
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Progress bar with enhanced styling
          _buildProgressBar(),

          const SizedBox(height: 12),

          // Speed control positioned on right side
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // Playback speed button with enhanced design (right corner)
              _buildSpeedButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: SliderTheme(
        data: SliderTheme.of(context).copyWith(
          activeTrackColor: Colors.red,
          inactiveTrackColor: Colors.white.withOpacity(0.3),
          thumbColor: Colors.red,
          thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
          overlayShape: const RoundSliderOverlayShape(overlayRadius: 12),
          trackHeight: 3,
          overlayColor: Colors.red.withOpacity(0.2),
          valueIndicatorShape: const PaddleSliderValueIndicatorShape(),
          valueIndicatorColor: Colors.red,
          valueIndicatorTextStyle: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        child: Slider(
          value: _duration > 0 ? _currentPosition.toDouble() : 0.0,
          max: _duration > 0 ? _duration.toDouble() : 1.0,
          onChanged: (value) {
            setState(() {
              _currentPosition = value.toInt();
            });
          },
          onChangeStart: (value) {
            _cancelAutoHideTimer(); // Cancel timer while seeking
          },
          onChangeEnd: (value) {
            _seekTo(value.toInt()); // Seek when dragging ends
            _startAutoHideTimer(); // Restart timer after seeking
          },
        ),
      ),
    );
  }

  Widget _buildSpeedButton() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          _showSpeedMenu();
          _startAutoHideTimer(); // Restart timer on interaction
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
          constraints: const BoxConstraints(
            minWidth: 48,
            minHeight: 48,
          ),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Center(
            child: Text(
              '${_currentPlaybackSpeed}x',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBufferingIndicator() {
    return Container(
      color: Colors.black54,
      child: const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 3,
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              color: Colors.white,
              strokeWidth: 3,
            ),
            const SizedBox(height: 16),
            const Text(
              'Loading video...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (widget.video.title.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                widget.video.title,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildErrorOverlay() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Video Unavailable',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _hasError = false;
                  _isLoading = true;
                });
                _initializePlayer();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Icon(
          Icons.error_outline,
          color: Colors.red,
          size: 64,
        ),
      ),
    );
  }

  void _showSpeedMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black87,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => _buildSpeedMenu(),
    );
  }

  Widget _buildSpeedMenu() {
    final speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

    return SafeArea(
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const Padding(
              padding: EdgeInsets.all(20),
              child: Text(
                'Playback Speed',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            ...speeds.map((speed) => Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  _changePlaybackSpeed(speed);
                  Navigator.pop(context);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  child: Row(
                    children: [
                      Text(
                        '${speed}x',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (speed == 1.0) ...[
                        const SizedBox(width: 8),
                        Text(
                          '(Normal)',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.6),
                            fontSize: 14,
                          ),
                        ),
                      ],
                      const Spacer(),
                      if (_currentPlaybackSpeed == speed)
                        const Icon(
                          Icons.check_circle,
                          color: Colors.red,
                          size: 20,
                        ),
                    ],
                  ),
                ),
              ),
            )),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildInteractiveTouchLayer() {
    return Positioned(
      top: 80, // Exclude top 80px for top controls (back, settings, fullscreen)
      left: 0,
      right: 0,
      bottom: 120, // Exclude bottom 120px for controls (speed button + seek bar)
      child: GestureDetector(
        onTap: () {
          // Show controls when tapping anywhere (if hidden)
          if (!_showControls) {
            _showControlsTemporarily();
          }
        },
        child: Row(
          children: [
            // Left side - Skip backward (30% of screen)
            Expanded(
              flex: 3,
              child: GestureDetector(
                onTap: () {
                  _skipBackward();
                  _showControlsTemporarily(); // Keep controls visible
                },
                behavior: HitTestBehavior.translucent,
                child: Container(
                  color: Colors.transparent,
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
            ),

            // Center area - Play/pause (40% of screen)
            Expanded(
              flex: 4,
              child: GestureDetector(
                onTap: () {
                  _togglePlayPause();
                  _showControlsTemporarily(); // Keep controls visible
                },
                behavior: HitTestBehavior.translucent,
                child: Container(
                  color: Colors.transparent,
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
            ),

            // Right side - Skip forward (30% of screen)
            Expanded(
              flex: 3,
              child: GestureDetector(
                onTap: () {
                  _skipForward();
                  _showControlsTemporarily(); // Keep controls visible
                },
                behavior: HitTestBehavior.translucent,
                child: Container(
                  color: Colors.transparent,
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkipIndicator(bool isForward) {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOutBack,
      left: isForward ? null : 50,
      right: isForward ? 50 : null,
      top: 0,
      bottom: 0,
      child: Center(
        child: AnimatedScale(
          scale: 1.0,
          duration: const Duration(milliseconds: 200),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isForward ? Icons.fast_forward : Icons.fast_rewind,
                  color: Colors.white,
                  size: 32,
                ),
                const SizedBox(height: 4),
                Text(
                  '10',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'seconds',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDuration(int seconds) {
    final duration = Duration(seconds: seconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }

  String _formatRemainingTime(int currentSeconds, int totalSeconds) {
    final remaining = totalSeconds - currentSeconds;
    if (remaining <= 0) return '-0:00';

    final duration = Duration(seconds: remaining);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '-${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '-${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }
}
